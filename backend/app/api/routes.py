"""
REST API路由
"""
from typing import List
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from app.models.schemas import (
    TaskSummary, TaskDetail, Config, ConfigUpdate,
    ErrorResponse
)
from app.services.task_service import get_tasks, get_task_detail, delete_task, delete_multiple_tasks
from app.services.config_service import get_config, update_config


api_router = APIRouter()


@api_router.get("/tasks")
async def get_task_list(
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量")
):
    """获取任务列表"""
    try:
        tasks = await get_tasks(limit=limit, offset=offset)
        
        # 转换为字典格式
        tasks_data = [task.dict() for task in tasks]
        
        return {
            "success": True,
            "data": tasks_data
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get tasks",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}")
async def get_task_details(task_id: str):
    """获取任务详情"""
    try:
        task_detail = await get_task_detail(task_id)
        
        if not task_detail:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )
        
        return {
            "success": True,
            "data": {
                "task": task_detail.task.dict(),
                "messages": [msg.dict() for msg in task_detail.messages]
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get task detail",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.delete("/tasks/{task_id}")
async def delete_task_endpoint(task_id: str):
    """删除任务"""
    try:
        success = await delete_task(task_id)

        if not success:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )

        return {
            "success": True,
            "message": "Task deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to delete task",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.post("/tasks/batch-delete")
async def delete_multiple_tasks_endpoint(request: dict):
    """批量删除任务"""
    try:
        task_ids = request.get("task_ids", [])

        if not task_ids:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    error="No task IDs provided",
                    code="INVALID_REQUEST"
                ).dict()
            )

        deleted_count = await delete_multiple_tasks(task_ids)

        return {
            "success": True,
            "message": f"Successfully deleted {deleted_count} tasks",
            "deleted_count": deleted_count
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to delete tasks",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/config")
async def get_system_config():
    """获取系统配置"""
    try:
        config = await get_config()
        
        return {
            "success": True,
            "data": config.dict()
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.put("/config")
async def update_system_config(config_update: ConfigUpdate):
    """更新系统配置"""
    try:
        await update_config(config_update)
        
        return {
            "success": True,
            "message": "Configuration updated successfully"
        }
    
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=ErrorResponse(
                error="Invalid configuration",
                code="INVALID_CONFIG",
                details={"validation_errors": e.errors()}
            ).dict()
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to update configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )
