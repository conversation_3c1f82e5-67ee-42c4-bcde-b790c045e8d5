"""
任务相关业务逻辑
"""
import uuid
from datetime import datetime
from typing import List, Optional
from pathlib import Path

from app.core.database import execute_query, execute_one, execute_update
from app.core.config import get_reports_dir
from app.models.schemas import Task, TaskSummary, TaskDetail, Message


async def create_task(title: str, task_id: str = None) -> Task:
    """创建新任务"""
    if task_id is None:
        task_id = str(uuid.uuid4())
    now = datetime.utcnow()

    await execute_update(
        "INSERT INTO tasks (id, title, created_at, updated_at) VALUES (?, ?, ?, ?)",
        [task_id, title, now, now]
    )

    return await get_task_by_id(task_id)


async def get_task_by_id(task_id: str) -> Optional[Task]:
    """根据ID获取任务"""
    result = await execute_one(
        "SELECT * FROM tasks WHERE id = ?",
        [task_id]
    )
    
    if result:
        return Task(**result)
    return None


async def get_or_create_task(task_id: str) -> Task:
    """获取或创建任务"""
    task = await get_task_by_id(task_id)
    if not task:
        task = await create_task(f"Report Task {task_id[:8]}", task_id)
    return task


async def update_task_report_path(task_id: str, report_path: str) -> None:
    """更新任务的报告文件路径"""
    await execute_update(
        "UPDATE tasks SET report_file_path = ?, updated_at = ? WHERE id = ?",
        [report_path, datetime.utcnow(), task_id]
    )


async def get_tasks(limit: int = 20, offset: int = 0) -> List[TaskSummary]:
    """获取任务列表"""
    # 获取任务基础信息
    tasks = await execute_query(
        "SELECT * FROM tasks ORDER BY updated_at DESC LIMIT ? OFFSET ?",
        [limit, offset]
    )
    
    # 为每个任务添加消息计数
    result = []
    for task in tasks:
        message_count = await execute_one(
            "SELECT COUNT(*) as count FROM messages WHERE task_id = ?",
            [task["id"]]
        )
        
        task_summary = TaskSummary(
            id=task["id"],
            title=task["title"],
            created_at=task["created_at"],
            updated_at=task["updated_at"],
            message_count=message_count["count"] if message_count else 0
        )
        result.append(task_summary)
    
    return result


async def get_task_detail(task_id: str) -> Optional[TaskDetail]:
    """获取任务详情"""
    # 获取任务信息
    task_data = await execute_one(
        "SELECT * FROM tasks WHERE id = ?",
        [task_id]
    )
    
    if not task_data:
        return None
    
    # 获取消息历史
    messages_data = await execute_query(
        "SELECT * FROM messages WHERE task_id = ? ORDER BY timestamp ASC",
        [task_id]
    )
    
    task = Task(**task_data)
    messages = [Message(**msg) for msg in messages_data]
    
    return TaskDetail(task=task, messages=messages)


async def delete_task(task_id: str) -> bool:
    """删除任务及其相关数据"""
    # 检查任务是否存在
    task = await get_task_by_id(task_id)
    if not task:
        return False

    # 删除任务相关的消息
    await execute_update(
        "DELETE FROM messages WHERE task_id = ?",
        [task_id]
    )

    # 删除任务记录
    await execute_update(
        "DELETE FROM tasks WHERE id = ?",
        [task_id]
    )

    # 删除报告文件（如果存在）
    if task.report_file_path:
        try:
            import shutil
            task_dir = Path(task.report_file_path)
            if task_dir.exists() and task_dir.is_dir():
                shutil.rmtree(task_dir)
        except Exception as e:
            # 文件删除失败不影响数据库删除
            print(f"Warning: Failed to delete report files for task {task_id}: {e}")

    return True


async def delete_multiple_tasks(task_ids: List[str]) -> int:
    """批量删除任务"""
    deleted_count = 0

    for task_id in task_ids:
        success = await delete_task(task_id)
        if success:
            deleted_count += 1

    return deleted_count


async def save_report_file(task_id: str, html_content: str) -> str:
    """保存报告HTML文件"""
    print(f"[DEBUG] TaskService: Saving report file for task {task_id}")
    print(f"[DEBUG] TaskService: HTML content length: {len(html_content)}")

    reports_dir = get_reports_dir()
    task_dir = reports_dir / task_id
    task_dir.mkdir(exist_ok=True)

    file_path = task_dir / "report.html"
    print(f"[DEBUG] TaskService: Saving to file path: {file_path}")

    with open(file_path, "w", encoding="utf-8") as f:
        f.write(html_content)

    print(f"[DEBUG] TaskService: File saved successfully")

    # 更新任务的报告路径
    await update_task_report_path(task_id, str(task_dir))
    print(f"[DEBUG] TaskService: Updated task report path in database")

    return str(file_path)
