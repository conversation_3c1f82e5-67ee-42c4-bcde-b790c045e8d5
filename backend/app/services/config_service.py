"""
配置相关业务逻辑
"""
from datetime import datetime
from typing import Dict, Any

from app.core.database import execute_one, execute_update
from app.core.config import settings, mask_api_key
from app.models.schemas import Config, ConfigUpdate


async def get_config() -> Config:
    """获取系统配置"""
    config_keys = [
        "llm_base_url", "llm_api_key", "llm_model", 
        "report_storage_path", "debug_mode"
    ]
    
    config_data = {}
    
    for key in config_keys:
        # 先从数据库获取
        result = await execute_one(
            "SELECT value FROM config WHERE key = ?",
            [key]
        )
        
        if result:
            value = result["value"]
        else:
            # 如果数据库中没有，使用默认配置
            if key == "llm_base_url":
                value = settings.LLM_BASE_URL
            elif key == "llm_api_key":
                value = settings.LLM_API_KEY
            elif key == "llm_model":
                value = settings.LLM_MODEL
            elif key == "report_storage_path":
                value = settings.REPORT_STORAGE_PATH
            elif key == "debug_mode":
                value = str(settings.DEBUG)
            else:
                value = ""
        
        # 处理特殊字段
        if key == "llm_api_key":
            config_data[key] = mask_api_key(value)
        elif key == "debug_mode":
            config_data[key] = value.lower() == "true" if isinstance(value, str) else bool(value)
        else:
            config_data[key] = value
    
    return Config(**config_data)


async def update_config(config_update: ConfigUpdate) -> None:
    """更新系统配置"""
    now = datetime.utcnow()
    
    config_dict = config_update.dict()
    
    for key, value in config_dict.items():
        # 将布尔值转换为字符串存储
        if isinstance(value, bool):
            value = str(value).lower()
        
        await execute_update(
            "INSERT OR REPLACE INTO config (key, value, updated_at) VALUES (?, ?, ?)",
            [key, str(value), now]
        )


async def get_llm_config() -> Dict[str, Any]:
    """获取LLM配置（用于内部调用）"""
    llm_base_url = await execute_one("SELECT value FROM config WHERE key = ?", ["llm_base_url"])
    llm_api_key = await execute_one("SELECT value FROM config WHERE key = ?", ["llm_api_key"])
    llm_model = await execute_one("SELECT value FROM config WHERE key = ?", ["llm_model"])
    
    return {
        "llm_base_url": llm_base_url["value"] if llm_base_url else settings.LLM_BASE_URL,
        "llm_api_key": llm_api_key["value"] if llm_api_key else settings.LLM_API_KEY,
        "llm_model": llm_model["value"] if llm_model else settings.LLM_MODEL,
    }
