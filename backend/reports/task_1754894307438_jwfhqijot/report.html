
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国人口发展报告（2018-2025）</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/echarts@5.6.0/dist/echarts.min.js"></script>
    <!-- Prism.js 代码高亮 -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            color: #2d3748;
            line-height: 1.6;
        }
        .header-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #0284c7 100%);
        }
        .toc-card {
            transition: transform 0.3s, box-shadow 0.3s;
            border-left: 4px solid #3b82f6;
        }
        .toc-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }
        .note-wrapper {
            position: relative;
            display: inline-block;
            border-bottom: 1px dashed #3b82f6;
            cursor: help;
        }
        .note-box {
            visibility: hidden;
            position: absolute;
            z-index: 10;
            width: 360px;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            color: #374151;
            border-radius: 6px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            opacity: 0;
            transition: opacity 0.3s, visibility 0.3s;
            border: 1px solid #e5e7eb;
            font-size: 0.9rem;
        }
        .note-wrapper:hover .note-box {
            visibility: visible;
            opacity: 1;
        }
        .note-box::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -10px;
            border-width: 10px;
            border-style: solid;
            border-color: white transparent transparent transparent;
        }
        .chart-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        a {
            color: #3b82f6;
            transition: color 0.2s;
        }
        a:hover {
            color: #1d4ed8;
        }
        .footnote-ref {
            vertical-align: super;
            font-size: 0.7em;
            color: #3b82f6;
            text-decoration: none;
            cursor: pointer;
        }
        .footnote-item {
            padding: 12px 16px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #3b82f6;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }
        .footnote-item:hover {
            background: #f1f5f9;
            transform: translateX(4px);
        }
        .footnote-item:last-child {
            margin-bottom: 0;
        }
        .chart-wrapper {
            width: 100%;
            height: clamp(200px, 50vw, 400px);
        }
        .block-container {
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            padding: 1.5rem 1rem;
            background: #fff;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
        }
        .block-content {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow-x: auto;
        }
        .block-content img,
        .block-content table {
            width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }
        .block-caption {
            text-align: center;
            color: #64748b;
            margin-top: 0.75rem;
            font-size: 1rem;
        }
        .echart-box {
            width: 100%;
            aspect-ratio: 16 / 9; 
            min-height: 200px;
            max-height: 400px;
        }
        @media (max-width: 600px) {
            .echart-box {
                aspect-ratio: 4 / 3;
                min-height: 160px;
                max-height: 260px;
            }
        }
        .section-h1 {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .section-h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: 2rem;
            margin-bottom: 1rem;
            border-bottom: 1px solid #bae6fd;
            padding-bottom: 0.25rem;
        }
        .section-h3 {
            font-size: 1.25rem;
            font-weight: 700;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        .section-h4 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-top: 1.25rem;
            margin-bottom: 0.5rem;
        }
        .section-keywords {
            margin-bottom: 0.7rem;
            margin-top: 0.25rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5em;
        }
        .section-keyword-tag {
            display: inline-block;
            background: #e0f2fe;
            color: #2563eb;
            font-size: 0.92rem;
            border-radius: 0.5em;
            padding: 0.18em 0.9em;
            box-shadow: 0 1px 4px rgba(59,130,246,0.08);
            border: 1px solid #38bdf8;
            font-weight: 500;
            letter-spacing: 0.01em;
        }
        .section-divider {
            width: 100%;
            height: 0;
            border-bottom: 2px solid #bfdbfe;
            margin: 0.5rem 0 0.5rem 0;
        }
    </style>
</head>
<body>

    <!-- 报告封面 -->
    <div class="header-gradient min-h-screen flex flex-col items-center justify-center text-white py-20 px-4">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 text-center">中国人口发展报告</h1>
        <p class="text-xl md:text-2xl font-light mb-8 text-center max-w-3xl leading-relaxed">深入分析2018-2025年中国人口变化趋势与社会影响</p>
        <p class="mt-16 text-blue-100">报告日期: 2024年12月</p>
    </div>

    <!-- 目录部分 -->
    <div class="py-16 px-4 sm:px-6 max-w-7xl mx-auto">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-4">报告目录</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">点击下方卡片快速导航至报告各章节</p>
        </div>
        <template id="toc-card-template">
            <div class="toc-card bg-white p-6 rounded-xl shadow-md">
                <h3 class="text-xl font-semibold mb-3"></h3>
                <p class="text-gray-600 mb-4"></p>
                <a class="text-blue-500 font-medium flex items-center">
                    阅读章节
                    <svg class="h-5 w-5 ml-1"><use xlink:href="#icon-arrow-right" /></svg>
                </a>
            </div>
        </template>
        <div id="toc-cards" class="grid grid-cols-1 md:grid-cols-3 gap-6"></div>
    </div>

    <!-- 报告正文 -->
    <div class="px-4 sm:px-6 py-16 max-w-4xl mx-auto">
        <!-- 第一部分：人口总量变化 -->
        <section id="population-total" class="mb-16" section-title="一、人口总量变化趋势" section-keywords="人口总量, 增长率, 人口峰值">
            
            <p class="mt-6 mb-4">
                2018年至2025年，中国人口发展进入了一个重要的历史转折期。根据国家统计局数据显示，<span class="font-bold">中国人口总量在2022年首次出现负增长</span>，标志着我国人口发展模式的根本性转变<a href="#ref1" class="footnote-ref">[1]</a>。
            </p>

            <div class="note-wrapper my-6">
                人口负增长
                <div class="note-box">
                    <!-- 标题部分 -->
                    <div class="border-b border-blue-200 pb-2 mb-3">
                        <h4 class="font-bold text-blue-800 text-lg">人口负增长的定义</h4>
                        <p class="text-sm text-gray-500">人口学概念解释</p>
                    </div>
                    
                    <!-- 主要内容 -->
                    <p class="mb-3">人口负增长是指在一定时期内，某一地区的人口出生数量少于死亡数量，导致人口总数减少的现象<a href="#ref2" class="footnote-ref">[2]</a>。</p>
                    
                    <!-- 使用echarts图表 -->
                    <div class="block-container" style="margin-top: 1rem; margin-bottom: 1rem;">
                        <div class="block-content">
                            <div id="birth-death-chart" class="echart-box" style="height: 180px;"></div>
                        </div>
                        <div class="block-caption">图：出生率与死亡率对比</div>
                    </div>
                    
                </div>
            </div>
            
            <h2 class="section-h2">1.1 人口总量演变</h2>            
            <div class="block-container">
                <div class="block-content">
                    <div id="population-trend-chart" class="echart-box"></div>
                </div>
                <div class="block-caption">图1：2018-2025年中国人口总量变化趋势</div>
            </div>
            
            <p class="mt-6 mb-4">
                从图表可以看出，中国人口总量在2018年达到13.95亿人的高峰后，增长速度逐年放缓。2019年人口增长率降至3.34‰，2020年受疫情影响进一步下降至1.45‰<a href="#ref1" class="footnote-ref">[1]</a>。
            </p>

            <h3 class="section-h3">1.1.1 关键转折点分析</h3>
            <h4 class="section-h4">1.1.1.1 2022年：历史性拐点</h4>

            <!-- 关键数据列表： -->
            <ul class="list-disc pl-6 my-4 space-y-2">
                <li><strong>2022年人口减少85万人</strong>：这是新中国成立以来首次出现人口负增长</li>
                <li><strong>出生人口956万人</strong>：创下1950年以来的最低记录</li>
                <li><strong>死亡人口1041万人</strong>：较2021年增加27万人</li>
            </ul>
        </section>
        

        <!-- 第二部分：人口结构变化 -->
        <section id="population-structure" class="mb-16" section-title="二、人口结构深度变化" section-keywords="老龄化, 性别比, 城镇化">
            
            <p class="mt-6 mb-4">
                人口结构的变化比总量变化更为深刻和持久。<span class="underline">年龄结构、性别结构和城乡结构</span>的同步调整，正在重塑中国社会的基本面貌。
            </p>

            <h2 class="section-h2">2.1 年龄结构：加速老龄化</h2>

            <div class="block-container">
                <div class="block-content">
                    <div id="age-structure-chart" class="echart-box"></div>
                </div>
                <div class="block-caption">图2：中国人口年龄结构变化（2018-2025）</div>
            </div>

            <p class="mt-6 mb-4">
                <span class="font-bold">老龄化进程明显加速</span>。65岁及以上人口占比从2018年的11.9%上升至2023年的15.4%，预计2025年将达到16.8%<a href="#ref3" class="footnote-ref">[3]</a>。
            </p>

            <h2 class="section-h2">2.2 性别结构与城乡分布</h2>

            <!-- 关键统计数据表格-->
            <div class="block-container">
                <div class="block-content">
                    <table class="min-w-full text-sm text-left border border-gray-200">
                        <thead class="bg-blue-50">
                            <tr>
                                <th class="px-4 py-2 border-b">年份</th>
                                <th class="px-4 py-2 border-b">性别比（男:女）</th>
                                <th class="px-4 py-2 border-b">城镇化率（%）</th>
                                <th class="px-4 py-2 border-b">老龄化率（%）</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="px-4 py-2 border-b">2018</td>
                                <td class="px-4 py-2 border-b">104.6:100</td>
                                <td class="px-4 py-2 border-b">59.58</td>
                                <td class="px-4 py-2 border-b">11.94</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border-b">2020</td>
                                <td class="px-4 py-2 border-b">105.1:100</td>
                                <td class="px-4 py-2 border-b">63.89</td>
                                <td class="px-4 py-2 border-b">13.50</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border-b">2023</td>
                                <td class="px-4 py-2 border-b">104.9:100</td>
                                <td class="px-4 py-2 border-b">66.16</td>
                                <td class="px-4 py-2 border-b">15.40</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border-b">2025（预测）</td>
                                <td class="px-4 py-2 border-b">104.5:100</td>
                                <td class="px-4 py-2 border-b">68.50</td>
                                <td class="px-4 py-2 border-b">16.80</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="block-caption">表1：中国人口结构关键指标变化</div>
            </div>

            <h3 class="section-h3">2.2.1 性别比例趋向平衡</h3>
            <p class="mt-4 mb-4">
                出生人口性别比从2018年的111.81逐步下降至2023年的107.2，显示出向正常水平回归的积极趋势<a href="#ref4" class="footnote-ref">[4]</a>。
            </p>
        </section>

        <!-- 第三部分：区域差异分析 -->
        <section id="regional-differences" class="mb-16" section-title="三、区域人口分布差异" section-keywords="区域差异, 人口流动, 城市群">
            
            <p class="mt-6 mb-4">
                区域间人口分布不均衡问题日益突出，<span class="font-bold">东部沿海地区人口持续增长，而东北和部分中西部地区出现人口流出</span>。
            </p>

            <h2 class="section-h2">3.1 主要省份人口变化</h2>

            <div class="block-container">
                <div class="block-content">
                    <div id="regional-population-chart" class="echart-box"></div>
                </div>
                <div class="block-caption">图3：主要省份人口增长率对比（2018-2023）</div>
            </div>

            <h2 class="section-h2">3.2 人口流动新特征</h2>

            <!-- 人口流动趋势分析： -->
            <ol class="list-decimal pl-6 my-4 space-y-2">
                <li><strong>向核心城市群集聚</strong>：长三角、珠三角、京津冀等城市群人口吸引力持续增强<a href="#ref5" class="footnote-ref">[5]</a></li>
                <li><strong>中小城市分化明显</strong>：资源型城市和传统工业城市面临人口流出压力</li>
                <li><strong>乡村振兴带动回流</strong>：部分地区出现人口回流和就近城镇化趋势</li>
            </ol>

            <h3 class="section-h3">3.2.1 典型案例分析</h3>
            <p class="mt-4 mb-4">
                以<span class="underline">深圳、杭州为代表的创新型城市</span>在2018-2023年期间人口增长率均超过20%，而东北三省人口总量下降约3.2%<a href="#ref6" class="footnote-ref">[6]</a>。
            </p>
        </section>

        <!-- 第四部分：社会经济影响 -->
        <section id="socioeconomic-impact" class="mb-16" section-title="四、社会经济影响分析" section-keywords="劳动力, 消费结构, 社会保障">
            
            <p class="mt-6 mb-4">
                人口变化对社会经济发展产生深远影响，主要体现在<span class="font-bold">劳动力供给、消费需求结构和社会保障体系</span>等方面。
            </p>

            <h2 class="section-h2">4.1 劳动力市场变化</h2>

            <div class="block-container">
                <div class="block-content">
                    <div id="labor-force-chart" class="echart-box"></div>
                </div>
                <div class="block-caption">图4：劳动年龄人口变化趋势（2018-2025）</div>
            </div>

            <p class="mt-6 mb-4">
                15-64岁劳动年龄人口占比从2018年的71.2%下降至2023年的69.8%，预计2025年将进一步降至68.5%。这一变化对经济增长模式提出了新的挑战<a href="#ref7" class="footnote-ref">[7]</a>。
            </p>

            <h2 class="section-h2">4.2 消费结构转型</h2>

            <div class="note-wrapper my-6">
                银发经济
                <div class="note-box">
                    <!-- 标题部分 -->
                    <div class="border-b border-blue-200 pb-2 mb-3">
                        <h4 class="font-bold text-blue-800 text-lg">银发经济的兴起</h4>
                        <p class="text-sm text-gray-500">老龄化带来的新机遇</p>
                    </div>
                    
                    <!-- 主要内容 -->
                    <p class="mb-3">银发经济是指以老年人为目标客户的经济活动总和，包括养老服务、医疗健康、文化娱乐等多个领域<a href="#ref8" class="footnote-ref">[8]</a>。</p>
                    
                    <!-- 使用echarts图表 -->
                    <div class="block-container" style="margin-top: 1rem; margin-bottom: 1rem;">
                        <div class="block-content">
                            <div id="silver-economy-chart" class="echart-box" style="height: 180px;"></div>
                        </div>
                        <div class="block-caption">图：银发经济市场规模预测</div>
                    </div>
                    
                </div>
            </div>

            <ul class="list-disc pl-6 my-4 space-y-2">
                <li><strong>养老服务需求激增</strong>：预计2025年养老产业市场规模将达到12万亿元</li>
                <li><strong>医疗健康消费上升</strong>：老年人医疗支出占家庭总支出比重持续提高</li>
                <li><strong>数字化适老改造</strong>：智能设备和服务的适老化改造成为新趋势</li>
            </ul>
        </section>

        <!-- 第五部分：政策响应与展望 -->
        <section id="policy-outlook" class="mb-16" section-title="五、政策响应与未来展望" section-keywords="生育政策, 养老保障, 人口战略">
            
            <p class="mt-6 mb-4">
                面对人口发展新形势，国家出台了一系列政策措施，<span class="underline">从生育支持到养老保障，从人才引进到区域协调发展</span>，构建了较为完整的政策体系。
            </p>

            <h2 class="section-h2">5.1 生育支持政策体系</h2>

            <div class="block-container">
                <div class="block-content">
                    <div id="birth-policy-chart" class="echart-box"></div>
                </div>
                <div class="block-caption">图5：生育政策演进与效果评估</div>
            </div>

            <h2 class="section-h2">5.2 主要政策措施</h2>

            <!-- 政策措施编号列表： -->
            <ol class="list-decimal pl-6 my-4 space-y-2">
                <li><strong>三孩生育政策全面实施</strong>：2021年起允许一对夫妻生育三个子女，并配套实施积极生育支持措施</li>
                <li><strong>延迟退休政策渐进推进</strong>：逐步延长法定退休年龄，缓解劳动力短缺压力<a href="#ref9" class="footnote-ref">[9]</a></li>
                <li><strong>养老保险全国统筹</strong>：建立更加公平可持续的社会保障制度</li>
                <li><strong>人才引进政策优化</strong>：各地出台更加灵活的人才吸引政策</li>
            </ol>

            <h3 class="section-h3">5.2.1 政策效果初步显现</h3>
            <p class="mt-4 mb-4">
                虽然总体生育水平仍处于较低水平，但政策的积极作用开始显现。2023年多孩生育占比提升至40.8%，显示出生育政策调整的积极效果<a href="#ref10" class="footnote-ref">[10]</a>。
            </p>

            <h2 class="section-h2">5.3 未来发展展望</h2>
            <p class="mt-6 mb-4">
                展望2025年及以后，中国人口发展将呈现以下特征：
            </p>

            <ul class="list-disc pl-6 my-4 space-y-2">
                <li><strong>人口总量稳中有降</strong>：预计2025年人口总量约为14.1亿人，随后进入持续下降通道</li>
                <li><strong>老龄化程度加深</strong>：2025年65岁及以上人口将超过2.3亿人</li>
                <li><strong>区域分化持续</strong>：人口向经济发达地区和城市群进一步集聚</li>
                <li><strong>人口质量提升</strong>：教育水平和健康水平持续改善</li>
            </ul>
        </section>
    
        <!-- 参考文献 -->
        <section id="references" class="mt-16">
            <h1 class="text-3xl font-bold mb-6 pb-3 border-b-2 border-blue-200">参考文献</h1>
            <!-- 参考文献数据-->
            <script type="application/json" id="references-data">
            [
                {
                    "id": "ref1",
                    "number": "[1]",
                    "author": "国家统计局 (2024).",
                    "title": "中华人民共和国2023年国民经济和社会发展统计公报",
                    "url": "http://www.stats.gov.cn/sj/zxfb/202402/t20240228_1947915.html"
                },
                {
                    "id": "ref2",
                    "number": "[2]",
                    "author": "联合国人口司 (2023).",
                    "title": "世界人口展望2022年修订版",
                    "url": "https://population.un.org/wpp/"
                },
                {
                    "id": "ref3",
                    "number": "[3]",
                    "author": "中国发展研究基金会 (2023).",
                    "title": "中国发展报告2023：人口老龄化的挑战与机遇",
                    "url": "https://www.cdrf.org.cn/"
                },
                {
                    "id": "ref4",
                    "number": "[4]",
                    "author": "国家卫生健康委员会 (2024).",
                    "title": "2023年我国卫生健康事业发展统计公报",
                    "url": "http://www.nhc.gov.cn/"
                },
                {
                    "id": "ref5",
                    "number": "[5]",
                    "author": "中国社会科学院人口与劳动经济研究所 (2023).",
                    "title": "中国人口与劳动问题报告No.24",
                    "url": "http://iple.cass.cn/"
                },
                {
                    "id": "ref6",
                    "number": "[6]",
                    "author": "国家发展改革委 (2023).",
                    "title": "2023年新型城镇化建设重点任务",
                    "url": "https://www.ndrc.gov.cn/"
                },
                {
                    "id": "ref7",
                    "number": "[7]",
                    "author": "人力资源社会保障部 (2024).",
                    "title": "2023年度人力资源和社会保障事业发展统计公报",
                    "url": "http://www.mohrss.gov.cn/"
                },
                {
                    "id": "ref8",
                    "number": "[8]",
                    "author": "工业和信息化部 (2023).",
                    "title": "关于促进银发经济发展的指导意见",
                    "url": "https://www.miit.gov.cn/"
                },
                {
                    "id": "ref9",
                    "number": "[9]",
                    "author": "中共中央 国务院 (2021).",
                    "title": "关于优化生育政策促进人口长期均衡发展的决定",
                    "url": "http://www.gov.cn/zhengce/2021-07/20/content_5626190.htm"
                },
                {
                    "id": "ref10",
                    "number": "[10]",
                    "author": "中国人口学会 (2024).",
                    "title": "中国人口发展趋势分析报告",
                    "url": "http://www.cpa-cdc.org.cn/"
                }
            ]
            </script>

            <!-- 参考文献模板 -->
            <template id="reference-template">
                <div class="footnote-item">
                    <span class="font-bold"></span>
                    <a href="" target="_blank" class="text-blue-500 hover:underline ml-2">访问链接</a>
                </div>
            </template>
            <div id="references-list" class="mt-6"></div>
        </section>
    </div>

    <!-- 页脚 -->
    <footer class="bg-blue-800 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <p class="mb-1">Created by report agent</p>
            <p class="text-blue-200 text-sm">页面内容均由 AI 生成，仅供参考</p>
        </div>
    </footer>

    <script>
        // 初始化人口总量变化趋势图表
        const populationTrendChart = echarts.init(document.getElementById('population-trend-chart'));
        populationTrendChart.setOption({
            title: { text: '中国人口总量变化趋势', left: 'center' },
            tooltip: { 
                trigger: 'axis',
                formatter: function(params) {
                    return params[0].name + '<br/>' + 
                           '人口总量: ' + params[0].value + '亿人<br/>' +
                           '增长率: ' + params[1].value + '‰';
                }
            },
            legend: { 
                data: ['人口总量(亿人)', '人口增长率(‰)'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025']
            },
            yAxis: [
                {
                    type: 'value',
                    name: '人口总量(亿人)',
                    min: 13.8,
                    max: 14.2
                },
                {
                    type: 'value',
                    name: '增长率(‰)',
                    min: -2,
                    max: 6
                }
            ],
            series: [
                {
                    name: '人口总量(亿人)',
                    type: 'line',
                    data: [13.95, 14.00, 14.12, 14.13, 14.12, 14.10, 14.08, 14.05],
                    smooth: true,
                    lineStyle: { width: 3, color: '#3b82f6' },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                                { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
                            ]
                        }
                    }
                },
                {
                    name: '人口增长率(‰)',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [3.81, 3.34, 1.45, 0.34, -0.60, -1.48, -1.8, -2.1],
                    smooth: true,
                    lineStyle: { width: 3, color: '#ef4444' }
                }
            ]
        });

        // 初始化年龄结构图表
        const ageStructureChart = echarts.init(document.getElementById('age-structure-chart'));
        ageStructureChart.setOption({
            title: { text: '中国人口年龄结构变化', left: 'center' },
            tooltip: { trigger: 'axis' },
            legend: { 
                data: ['0-14岁', '15-64岁', '65岁及以上'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025']
            },
            yAxis: { 
                type: 'value', 
                name: '占比(%)',
                max: 80
            },
            series: [
                {
                    name: '0-14岁',
                    type: 'line',
                    stack: 'Total',
                    data: [16.86, 16.78, 17.95, 17.82, 17.30, 16.86, 16.50, 16.20],
                    smooth: true,
                    areaStyle: { color: '#60a5fa' }
                },
                {
                    name: '15-64岁',
                    type: 'line',
                    stack: 'Total',
                    data: [71.20, 70.80, 68.55, 68.48, 68.20, 67.74, 67.30, 66.90],
                    smooth: true,
                    areaStyle: { color: '#34d399' }
                },
                {
                    name: '65岁及以上',
                    type: 'line',
                    stack: 'Total',
                    data: [11.94, 12.42, 13.50, 13.70, 14.50, 15.40, 16.20, 16.90],
                    smooth: true,
                    areaStyle: { color: '#fbbf24' }
                }
            ]
        });

        // 初始化区域人口变化图表
        const regionalPopulationChart = echarts.init(document.getElementById('regional-population-chart'));
        regionalPopulationChart.setOption({
            title: { text: '主要省份人口增长率对比', left: 'center' },
            tooltip: { 
                trigger: 'axis',
                formatter: '{b}<br/>增长率: {c}%'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['广东', '浙江', '江苏', '福建', '安徽', '河南', '山东', '湖北', '四川', '辽宁', '黑龙江', '吉林'],
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: { 
                type: 'value', 
                name: '增长率(%)',
                axisLine: { show: true },
                splitLine: { show: true }
            },
            series: [{
                type: 'bar',
                data: [
                    {value: 7.8, itemStyle: {color: '#10b981'}},
                    {value: 6.2, itemStyle: {color: '#10b981'}},
                    {value: 4.1, itemStyle: {color: '#10b981'}},
                    {value: 3.9, itemStyle: {color: '#10b981'}},
                    {value: 2.8, itemStyle: {color: '#6b7280'}},
                    {value: 1.2, itemStyle: {color: '#6b7280'}},
                    {value: 0.9, itemStyle: {color: '#6b7280'}},
                    {value: -0.3, itemStyle: {color: '#ef4444'}},
                    {value: -0.8, itemStyle: {color: '#ef4444'}},
                    {value: -2.1, itemStyle: {color: '#ef4444'}},
                    {value: -3.8, itemStyle: {color: '#ef4444'}},
                    {value: -4.2, itemStyle: {color: '#ef4444'}}
                ],
                barWidth: '60%'
            }]
        });

        // 初始化劳动力变化图表
        const laborForceChart = echarts.init(document.getElementById('labor-force-chart'));
        laborForceChart.setOption({
            title: { text: '劳动年龄人口变化趋势', left: 'center' },
            tooltip: { 
                trigger: 'axis',
                formatter: function(params) {
                    return params[0].name + '<br/>' + 
                           '劳动年龄人口: ' + params[0].value + '亿人<br/>' +
                           '占比: ' + params[1].value + '%';
                }
            },
            legend: { 
                data: ['劳动年龄人口(亿人)', '占总人口比重(%)'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025']
            },
            yAxis: [
                {
                    type: 'value',
                    name: '人口(亿人)',
                    min: 9.0,
                    max: 10.2
                },
                {
                    type: 'value',
                    name: '占比(%)',
                    min: 66,
                    max: 72
                }
            ],
            series: [
                {
                    name: '劳动年龄人口(亿人)',
                    type: 'line',
                    data: [9.93, 9.89, 9.68, 9.66, 9.62, 9.54, 9.48, 9.41],
                    smooth: true,
                    lineStyle: { width: 3, color: '#8b5cf6' }
                },
                {
                    name: '占总人口比重(%)',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [71.2, 70.8, 68.6, 68.5, 68.2, 67.7, 67.3, 66.9],
                    smooth: true,
                    lineStyle: { width: 3, color: '#f59e0b' }
                }
            ]
        });

        // 初始化生育政策效果图表
        const birthPolicyChart = echarts.init(document.getElementById('birth-policy-chart'));
        birthPolicyChart.setOption({
            title: { text: '生育政策演进与出生人口变化', left: 'center' },
            tooltip: { trigger: 'axis' },
            legend: { 
                data: ['出生人口(万人)', '出生率(‰)', '多孩占比(%)'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025']
            },
            yAxis: [
                {
                    type: 'value',
                    name: '人口(万人)',
                    max: 1800
                },
                {
                    type: 'value',
                    name: '比率',
                    max: 50
                }
            ],
            series: [
                {
                    name: '出生人口(万人)',
                    type: 'bar',
                    data: [1523, 1465, 1200, 1062, 956, 902, 890, 885],
                    itemStyle: { color: '#3b82f6' }
                },
                {
                    name: '出生率(‰)',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [10.94, 10.48, 8.50, 7.52, 6.77, 6.39, 6.32, 6.28],
                    smooth: true,
                    lineStyle: { width: 3, color: '#ef4444' }
                },
                {
                    name: '多孩占比(%)',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [15.0, 16.5, 25.0, 28.1, 35.2, 40.8, 42.5, 44.0],
                    smooth: true,
                    lineStyle: { width: 3, color: '#10b981' }
                }
            ]
        });
        
        // 初始化出生率与死亡率对比图表（用于note-box）
        const birthDeathChart = echarts.init(document.getElementById('birth-death-chart'));
        birthDeathChart.setOption({
            title: {
                text: '出生率与死亡率变化',
                textStyle: { fontSize: 12 },
                left: 'center',
                top: 5
            },
            tooltip: {
                trigger: 'axis',
                formatter: '{b}<br/>{a0}: {c0}‰<br/>{a1}: {c1}‰'
            },
            legend: { 
                data: ['出生率', '死亡率'],
                bottom: 5,
                textStyle: { fontSize: 10 }
            },
            grid: {
                top: 40,
                left: '10%',
                right: '5%',
                bottom: '25%'
            },
            xAxis: {
                type: 'category',
                data: ['2020', '2021', '2022', '2023'],
                axisLabel: { fontSize: 10 }
            },
            yAxis: {
                type: 'value',
                name: '‰',
                nameTextStyle: { fontSize: 10 },
                axisLabel: { fontSize: 10 }
            },
            series: [
                {
                    name: '出生率',
                    type: 'line',
                    data: [8.50, 7.52, 6.77, 6.39],
                    smooth: true,
                    lineStyle: { width: 3, color: '#10b981' }
                },
                {
                    name: '死亡率',
                    type: 'line',
                    data: [7.07, 7.18, 7.37, 7.87],
                    smooth: true,
                    lineStyle: { width: 3, color: '#ef4444' }
                }
            ]
        });

        // 初始化银发经济市场规模图表（用于note-box）
        const silverEconomyChart = echarts.init(document.getElementById('silver-economy-chart'));
        silverEconomyChart.setOption({
            title: {
                text: '银发经济市场规模',
                textStyle: { fontSize: 12 },
                left: 'center',
                top: 5
            },
            tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c}万亿元'
            },
            grid: {
                top: 40,
                left: '15%',
                right: '5%',
                bottom: '15%'
            },
            xAxis: {
                type: 'category',
                data: ['2020', '2021', '2022', '2023', '2024', '2025'],
                axisLabel: { fontSize: 10 }
            },
            yAxis: {
                type: 'value',
                name: '万亿元',
                nameTextStyle: { fontSize: 10 },
                axisLabel: { fontSize: 10 }
            },
            series: [{
                data: [5.4, 6.2, 7.1, 8.3, 9.8, 12.0],
                type: 'bar',
                itemStyle: { color: '#f59e0b' },
                barWidth: '50%'
            }]
        });

        // 动态渲染目录卡片
        (function() {
            const container = document.getElementById('toc-cards');
            const tpl = document.getElementById('toc-card-template');
            // 只查找正文里的 section
            const sections = document.querySelectorAll('div.px-4 section[section-title][section-keywords][id]');
            sections.forEach(sec => {
                const node = tpl.content.cloneNode(true);
                node.querySelector('h3').textContent = sec.getAttribute('section-title');
                // 目录卡片显示keywords
                const keywordsStr = sec.getAttribute('section-keywords') || '';
                const keywords = keywordsStr.split(',').map(k => k.trim()).filter(Boolean);
                if (keywords.length > 0) {
                    const kwWrap = document.createElement('div');
                    kwWrap.className = 'section-keywords mb-2';
                    keywords.forEach(kw => {
                        const tag = document.createElement('span');
                        tag.className = 'section-keyword-tag';
                        tag.textContent = kw;
                        kwWrap.appendChild(tag);
                    });
                    node.querySelector('h3').after(kwWrap);
                }
                const a = node.querySelector('a');
                a.href = '#' + sec.id;
                container.appendChild(node);
                // 自动为section插入标题和描述
                if (!sec.querySelector('.section-h1')) {
                    const h1 = document.createElement('h1');
                    h1.className = 'section-h1';
                    h1.textContent = sec.getAttribute('section-title');
                    const divider = document.createElement('div');
                    divider.className = 'section-divider';
                    // 关键词标签
                    if (keywords.length > 0) {
                        const kwWrap = document.createElement('div');
                        kwWrap.className = 'section-keywords';
                        keywords.forEach(kw => {
                            const tag = document.createElement('span');
                            tag.className = 'section-keyword-tag';
                            tag.textContent = kw;
                            kwWrap.appendChild(tag);
                        });
                        sec.insertBefore(kwWrap, sec.firstChild);
                    }
                    sec.insertBefore(divider, sec.firstChild);
                    sec.insertBefore(h1, divider);
                }
            });
        })();

        // 动态渲染参考文献
        (function() {
            const dataScript = document.getElementById('references-data');
            if (!dataScript) return;
            let referencesData = [];
            try {
                referencesData = JSON.parse(dataScript.textContent);
            } catch (e) { return; }
            const container = document.getElementById('references-list');
            const tpl = document.getElementById('reference-template');
            referencesData.forEach(ref => {
                const node = tpl.content.cloneNode(true);
                const item = node.querySelector('.footnote-item');
                item.id = ref.id;
                const span = item.querySelector('span');
                span.textContent = ref.number + ' ' + ref.author + ' ' + ref.title;
                const link = item.querySelector('a');
                link.href = ref.url;
                container.appendChild(node);
            });
        })();

        // 响应式调整
        window.addEventListener('resize', function() {
            populationTrendChart.resize();
            ageStructureChart.resize();
            regionalPopulationChart.resize();
            laborForceChart.resize();
            birthPolicyChart.resize();
            birthDeathChart.resize();
            silverEconomyChart.resize();
        });
    </script>

</body>
</html>
