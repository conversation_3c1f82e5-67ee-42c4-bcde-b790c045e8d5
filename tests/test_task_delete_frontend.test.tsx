/**
 * 测试前端任务删除功能
 */
import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi, describe, it, expect, beforeEach } from 'vitest'

// Mock zustand store
const mockDeleteTask = vi.fn()
const mockTasks = [
  {
    id: 'task1',
    title: '测试任务1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    message_count: 5,
    report_file_path: null,
    report_html_filename: 'report.html'
  },
  {
    id: 'task2',
    title: '测试任务2',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    message_count: 3,
    report_file_path: null,
    report_html_filename: 'report.html'
  }
]

vi.mock('../frontend/src/stores/taskStore', () => ({
  useTaskStore: () => ({
    tasks: mockTasks,
    currentTask: mockTasks[0],
    loading: false,
    error: null,
    fetchTasks: vi.fn(),
    selectTask: vi.fn(),
    createTask: vi.fn(),
    deleteTask: mockDeleteTask,
    setSearch: vi.fn()
  })
}))

// Import components after mocking
import TaskItem from '../frontend/src/components/sidebar/TaskItem'
import TaskList from '../frontend/src/components/sidebar/TaskList'
import TaskHistory from '../frontend/src/components/sidebar/TaskHistory'

describe('TaskItem删除功能', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该显示删除按钮当提供onDelete回调时', () => {
    const mockOnClick = vi.fn()
    const mockOnDelete = vi.fn()

    render(
      <TaskItem
        task={mockTasks[0]}
        isActive={true}
        onClick={mockOnClick}
        onDelete={mockOnDelete}
      />
    )

    // 删除按钮应该在激活状态下显示
    const deleteButton = screen.getByTitle('删除任务')
    expect(deleteButton).toBeInTheDocument()
  })

  it('应该不显示删除按钮当没有提供onDelete回调时', () => {
    const mockOnClick = vi.fn()

    render(
      <TaskItem
        task={mockTasks[0]}
        isActive={true}
        onClick={mockOnClick}
      />
    )

    // 删除按钮不应该显示
    const deleteButton = screen.queryByTitle('删除任务')
    expect(deleteButton).not.toBeInTheDocument()
  })

  it('应该调用onDelete当点击删除按钮时', async () => {
    const mockOnClick = vi.fn()
    const mockOnDelete = vi.fn()

    render(
      <TaskItem
        task={mockTasks[0]}
        isActive={true}
        onClick={mockOnClick}
        onDelete={mockOnDelete}
      />
    )

    const deleteButton = screen.getByTitle('删除任务')
    fireEvent.click(deleteButton)

    expect(mockOnDelete).toHaveBeenCalledWith('task1')
    expect(mockOnClick).not.toHaveBeenCalled() // 确保点击删除按钮不会触发任务选择
  })
})

describe('TaskList删除功能', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该将onTaskDelete传递给TaskItem组件', () => {
    const mockOnTaskSelect = vi.fn()
    const mockOnTaskDelete = vi.fn()

    render(
      <TaskList
        tasks={mockTasks}
        currentTaskId="task1"
        onTaskSelect={mockOnTaskSelect}
        onTaskDelete={mockOnTaskDelete}
      />
    )

    // 验证删除按钮存在（说明onDelete被正确传递）
    const deleteButtons = screen.getAllByTitle('删除任务')
    expect(deleteButtons).toHaveLength(2) // 两个任务都应该有删除按钮
  })
})

describe('TaskHistory删除功能集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该调用store的deleteTask方法当删除任务时', async () => {
    render(<TaskHistory />)

    // 找到第一个任务的删除按钮
    const deleteButtons = screen.getAllByTitle('删除任务')
    fireEvent.click(deleteButtons[0])

    await waitFor(() => {
      expect(mockDeleteTask).toHaveBeenCalledWith('task1')
    })
  })
})
