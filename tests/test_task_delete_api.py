"""
测试任务删除API功能
"""
import pytest
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

from app.services.task_service import create_task, delete_task, get_task_by_id, delete_multiple_tasks
from app.core.database import init_database


class TestTaskDeleteAPI:
    """测试任务删除API功能"""
    
    @pytest.fixture(autouse=True)
    async def setup_database(self):
        """设置测试数据库"""
        await init_database()
        yield
        # 测试后清理可以在这里添加
    
    @pytest.mark.asyncio
    async def test_delete_single_task(self):
        """测试删除单个任务"""
        # 创建测试任务
        task = await create_task("测试删除任务")
        task_id = task.id
        
        # 验证任务存在
        existing_task = await get_task_by_id(task_id)
        assert existing_task is not None
        assert existing_task.title == "测试删除任务"
        
        # 删除任务
        success = await delete_task(task_id)
        assert success is True
        
        # 验证任务已被删除
        deleted_task = await get_task_by_id(task_id)
        assert deleted_task is None
    
    @pytest.mark.asyncio
    async def test_delete_nonexistent_task(self):
        """测试删除不存在的任务"""
        # 尝试删除不存在的任务
        success = await delete_task("nonexistent_task_id")
        assert success is False
    
    @pytest.mark.asyncio
    async def test_delete_multiple_tasks(self):
        """测试批量删除任务"""
        # 创建多个测试任务
        task1 = await create_task("测试任务1")
        task2 = await create_task("测试任务2")
        task3 = await create_task("测试任务3")
        
        task_ids = [task1.id, task2.id, task3.id]
        
        # 验证任务存在
        for task_id in task_ids:
            task = await get_task_by_id(task_id)
            assert task is not None
        
        # 批量删除任务
        deleted_count = await delete_multiple_tasks(task_ids)
        assert deleted_count == 3
        
        # 验证任务已被删除
        for task_id in task_ids:
            task = await get_task_by_id(task_id)
            assert task is None
    
    @pytest.mark.asyncio
    async def test_delete_mixed_existing_nonexistent_tasks(self):
        """测试删除混合存在和不存在的任务"""
        # 创建一个测试任务
        task = await create_task("存在的任务")
        existing_task_id = task.id
        nonexistent_task_id = "nonexistent_task_id"
        
        task_ids = [existing_task_id, nonexistent_task_id]
        
        # 批量删除任务
        deleted_count = await delete_multiple_tasks(task_ids)
        assert deleted_count == 1  # 只有一个任务被成功删除
        
        # 验证存在的任务已被删除
        deleted_task = await get_task_by_id(existing_task_id)
        assert deleted_task is None


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
