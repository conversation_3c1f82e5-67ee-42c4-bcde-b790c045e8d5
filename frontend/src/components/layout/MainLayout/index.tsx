import React, { useState, useEffect } from 'react'
import clsx from 'clsx'
import SidebarPanel from '../SidebarPanel'
import ReportViewerPanel from '../ReportViewerPanel'
import ChatPanel from '../ChatPanel'
import MobileBottomNav from '../../mobile/MobileBottomNav'
import type { MainLayoutProps } from '../../../types/component'

type ActivePanel = 'sidebar' | 'report' | 'chat'

const MainLayout: React.FC<MainLayoutProps> = ({ className = '' }) => {
  const [activePanel, setActivePanel] = useState<ActivePanel>('chat')
  const [isMobile, setIsMobile] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  const handlePanelChange = (panel: ActivePanel) => {
    setActivePanel(panel)
  }

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <div
      className={clsx('h-screen bg-gray-50 overflow-hidden', className)}
      style={{
        display: 'grid',
        gridTemplateColumns: isMobile
          ? '1fr'
          : sidebarCollapsed
            ? '64px 1fr 500px'
            : '320px 1fr 500px',
        gridTemplateRows: '1fr',
        width: '100vw',
        maxWidth: '100vw'
      }}
    >
      {/* 左侧栏 */}
      <div
        className={clsx(
          'bg-white border-r border-gray-200 overflow-hidden',
          // 移动端显示控制
          isMobile ? (activePanel === 'sidebar' ? 'block' : 'hidden') : 'block'
        )}
        style={{ gridColumn: '1', gridRow: '1' }}
      >
        <SidebarPanel
          collapsed={sidebarCollapsed}
          onToggle={toggleSidebar}
          isMobile={isMobile}
        />
      </div>

      {/* 中间报告区 */}
      <div
        className={clsx(
          'overflow-hidden',
          // 移动端显示控制
          isMobile ? (activePanel === 'report' ? 'block' : 'hidden') : 'block'
        )}
        style={{ gridColumn: '2', gridRow: '1' }}
      >
        <ReportViewerPanel />
      </div>

      {/* 右侧聊天区 */}
      <div
        className={clsx(
          'bg-white border-l border-gray-200 overflow-hidden w-full',
          // 移动端显示控制
          isMobile ? (activePanel === 'chat' ? 'block' : 'hidden') : 'block'
        )}
        style={{ gridColumn: '3', gridRow: '1' }}
      >
        <ChatPanel />
      </div>

      {/* 移动端底部导航 */}
      {isMobile && (
        <MobileBottomNav
          activePanel={activePanel}
          onPanelChange={handlePanelChange}
        />
      )}
    </div>
  )
}

export default MainLayout
