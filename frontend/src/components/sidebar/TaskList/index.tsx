import React, { useRef, useCallback } from 'react'
import clsx from 'clsx'
import TaskItem from '../TaskItem'
import type { TaskListProps } from '../../../types/component'

const TaskList: React.FC<TaskListProps> = ({
  tasks,
  currentTaskId,
  onTaskSelect,
  onTaskDelete,
  className = ''
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const handleTaskSelect = useCallback((taskId: string) => {
    // 保存当前滚动位置
    const scrollTop = scrollContainerRef.current?.scrollTop || 0

    // 执行任务选择
    onTaskSelect(taskId)

    // 在下一个事件循环中恢复滚动位置
    setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTop = scrollTop
      }
    }, 0)
  }, [onTaskSelect])
  if (tasks.length === 0) {
    return (
      <div className={clsx('flex flex-col items-center justify-center h-full p-8', className)}>
        <div className="text-center">
          <svg
            className="w-12 h-12 text-gray-400 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="text-sm font-medium text-gray-900 mb-1">
            暂无任务
          </h3>
          <p className="text-sm text-gray-500">
            点击上方按钮创建第一个任务
          </p>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={scrollContainerRef}
      className={clsx('h-full overflow-y-auto custom-scrollbar', className)}
    >
      <div className="p-2 space-y-1">
        {tasks.map((task) => (
          <TaskItem
            key={task.id}
            task={task}
            isActive={task.id === currentTaskId}
            onClick={handleTaskSelect}
            onDelete={onTaskDelete}
          />
        ))}
      </div>
    </div>
  )
}

export default TaskList
