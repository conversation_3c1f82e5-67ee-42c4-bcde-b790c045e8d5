import React, { useEffect, useState } from 'react'
import clsx from 'clsx'
import { useTaskStore } from '../../../stores'
import TaskList from '../TaskList'
import TaskSearch from '../TaskSearch'
import NewTaskButton from '../NewTaskButton'
import Loading from '../../common/Loading'

interface TaskHistoryProps {
  collapsed?: boolean
  className?: string
}

const TaskHistory: React.FC<TaskHistoryProps> = ({
  collapsed = false,
  className = ''
}) => {
  const {
    tasks,
    currentTask,
    loading,
    error,
    fetchTasks,
    selectTask,
    createTask,
    deleteTask,
    setSearch
  } = useTaskStore()

  const [searchQuery, setSearchQuery] = useState('')

  // 组件挂载时获取任务列表
  useEffect(() => {
    fetchTasks()
  }, [fetchTasks])

  const handleTaskSelect = async (taskId: string) => {
    try {
      await selectTask(taskId)
    } catch (error) {
      console.error('Failed to select task:', error)
    }
  }

  const handleCreateTask = async () => {
    try {
      const title = `新任务 - ${new Date().toLocaleString()}`
      await createTask(title)
    } catch (error) {
      console.error('Failed to create task:', error)
    }
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setSearch(query)
  }

  const handleSearchClear = () => {
    setSearchQuery('')
    setSearch('')
  }

  const handleTaskDelete = async (taskId: string) => {
    try {
      await deleteTask(taskId)
    } catch (error) {
      console.error('Failed to delete task:', error)
    }
  }

  if (collapsed) {
    return (
      <div className={clsx('flex flex-col items-center py-4 space-y-4', className)}>
        {/* 折叠状态下的新建按钮 */}
        <button
          onClick={handleCreateTask}
          className="p-3 rounded-lg bg-primary-600 text-white hover:bg-primary-700 transition-colors"
          title="新建任务"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>

        {/* 折叠状态下的任务指示器 */}
        <div className="flex flex-col items-center space-y-2">
          {tasks.slice(0, 3).map((task) => (
            <button
              key={task.id}
              onClick={() => handleTaskSelect(task.id)}
              className={clsx(
                'w-8 h-8 rounded-lg flex items-center justify-center text-xs font-medium transition-colors',
                currentTask?.id === task.id
                  ? 'bg-primary-100 text-primary-700 border-2 border-primary-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              )}
              title={task.title}
            >
              {task.title.charAt(0).toUpperCase()}
            </button>
          ))}
          
          {tasks.length > 3 && (
            <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center text-xs text-gray-500">
              +{tasks.length - 3}
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={clsx('flex flex-col h-full', className)}>
      {/* 搜索和新建按钮 */}
      <div className="p-4 space-y-3">
        <TaskSearch
          value={searchQuery}
          onSearch={handleSearch}
          onClear={handleSearchClear}
          placeholder="搜索任务..."
        />
        <NewTaskButton onClick={handleCreateTask} />
      </div>

      {/* 任务列表 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <Loading message="加载任务中..." size="sm" />
          </div>
        ) : error ? (
          <div className="p-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-800">{error}</p>
              <button
                onClick={() => fetchTasks()}
                className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
              >
                重试
              </button>
            </div>
          </div>
        ) : (
          <TaskList
            tasks={tasks}
            currentTaskId={currentTask?.id}
            onTaskSelect={handleTaskSelect}
            onTaskDelete={handleTaskDelete}
          />
        )}
      </div>

      {/* 底部统计信息 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          共 {tasks.length} 个任务
        </div>
      </div>
    </div>
  )
}

export default TaskHistory
