import React, { useEffect, useRef } from 'react'
import clsx from 'clsx'
import MessageItem from '../MessageItem'
import TypingIndicator from '../TypingIndicator'
import type { MessageListProps } from '../../../types/component'

const MessageList: React.FC<MessageListProps> = ({
  messages,
  loading = false,
  isGeneratingReport = false,
  onRetry,
  className = ''
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  if (messages.length === 0 && !loading) {
    return (
      <div className={clsx('flex flex-col items-center justify-center h-full p-8', className)}>
        <div className="text-center max-w-sm">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            开始对话
          </h3>
          <p className="text-sm text-gray-600 leading-relaxed">
            我是您的AI助手，可以帮助您生成数据分析报告。请告诉我您需要什么样的报告，我会为您创建详细的HTML报告。
          </p>
          <div className="mt-4 space-y-2 text-xs text-gray-500">
            <p>💡 您可以：</p>
            <ul className="space-y-1">
              <li>• 描述您的数据分析需求</li>
              <li>• 选择报告中的特定区域进行讨论</li>
              <li>• 要求修改或优化报告内容</li>
            </ul>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={clsx('flex flex-col h-full overflow-y-auto custom-scrollbar', className)}
    >
      <div className="flex-1 p-4 space-y-4">
        {messages.map((message) => (
          <MessageItem
            key={message.id}
            message={message}
            onRetry={onRetry}
          />
        ))}

        {/* 输入指示器 */}
        {loading && <TypingIndicator />}

        {/* 报告生成指示器 */}
        {isGeneratingReport && (
          <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-900">正在生成报告...</p>
              <p className="text-xs text-blue-700">请稍候，AI正在为您创建详细的分析报告</p>
            </div>
          </div>
        )}

        {/* 滚动锚点 */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}

export default MessageList
