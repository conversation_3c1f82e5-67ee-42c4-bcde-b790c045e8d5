import React, { useState } from 'react'
import clsx from 'clsx'
import type { MessageItemProps } from '../../../types/component'

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  onRetry,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false)

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleRetry = () => {
    if (onRetry) {
      onRetry(message.id)
    }
  }

  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  const isSystem = message.role === 'system'

  return (
    <div
      className={clsx('flex', {
        'justify-end': isUser,
        'justify-start': isAssistant || isSystem
      }, className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={clsx('flex space-x-2', {
          'flex-row-reverse space-x-reverse': isUser
        })}
        style={{
          maxWidth: '360px',
          width: 'fit-content'
        }}
      >
        {/* 头像 */}
        <div className="flex-shrink-0">
          {isUser ? (
            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
          ) : isAssistant ? (
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
            </div>
          ) : (
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          )}
        </div>

        {/* 消息内容 */}
        <div className="flex flex-col space-y-1 w-full">
          {/* 消息气泡 */}
          <div
            className={clsx('px-4 py-2 rounded-2xl', {
              'bg-primary-600 text-white': isUser,
              'bg-gray-100 text-gray-900': isAssistant,
              'bg-yellow-50 text-yellow-800 border border-yellow-200': isSystem
            })}
          >
            <div className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </div>

            {/* 代码片段指示器 */}
            {message.has_code_snippet && isAssistant && (
              <div className="mt-2 flex items-center space-x-1 text-xs text-gray-500">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                  />
                </svg>
                <span>已生成报告</span>
              </div>
            )}
          </div>

          {/* 消息元信息 */}
          <div
            className={clsx('flex items-center space-x-2 text-xs text-gray-500', {
              'justify-end': isUser,
              'justify-start': isAssistant || isSystem
            })}
          >
            <span>{formatTime(message.timestamp)}</span>

            {/* 操作按钮 */}
            {isHovered && (
              <div className="flex items-center space-x-1">
                {/* 复制按钮 */}
                <button
                  onClick={() => navigator.clipboard.writeText(message.content)}
                  className="p-1 rounded hover:bg-gray-200 transition-colors"
                  title="复制消息"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </button>

                {/* 重试按钮（仅对助手消息显示） */}
                {isAssistant && onRetry && (
                  <button
                    onClick={handleRetry}
                    className="p-1 rounded hover:bg-gray-200 transition-colors"
                    title="重新生成"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MessageItem
