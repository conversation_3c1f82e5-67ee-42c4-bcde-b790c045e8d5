import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type { ReportStore } from '../types/store'

export const useReportStore = create<ReportStore>()(
  devtools(
    immer((set, get) => ({
      // 初始状态
      reportHtml: '',
      reportPath: null,
      selectedArea: null,
      selectionRange: null,
      isGenerating: false,
      generationProgress: 0,
      zoomLevel: 100,
      viewMode: 'fit',
      error: null,

      // 加载报告
      loadReport: async (reportPath) => {
        try {
          // 这里应该调用API获取报告内容
          // 暂时直接设置路径，实际内容由其他方式更新
          set((state) => {
            state.reportPath = reportPath
            state.error = null
          })
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load report'
          })
        }
      },

      // 更新报告内容
      updateReport: (html) => {
        set((state) => {
          state.reportHtml = html
        })
      },

      // 清空报告
      clearReport: () => {
        set((state) => {
          state.reportHtml = ''
          state.reportPath = null
          state.selectedArea = null
          state.selectionRange = null
          state.error = null
        })
      },

      // 选择区域
      selectArea: (html, range) => {
        set((state) => {
          state.selectedArea = html
          state.selectionRange = range || null
        })
      },

      // 清除选择
      clearSelection: () => {
        set((state) => {
          state.selectedArea = null
          state.selectionRange = null
        })
      },

      // 开始生成
      startGeneration: () => {
        set((state) => {
          state.isGenerating = true
          state.generationProgress = 0
          state.error = null
        })
      },

      // 更新进度
      updateProgress: (progress) => {
        set((state) => {
          state.generationProgress = Math.max(0, Math.min(100, progress))
        })
      },

      // 完成生成
      finishGeneration: (reportPath) => {
        set((state) => {
          state.isGenerating = false
          state.generationProgress = 100
          state.reportPath = reportPath
        })
      },

      // 设置缩放级别
      setZoomLevel: (level) => {
        set((state) => {
          state.zoomLevel = Math.max(25, Math.min(200, level))
        })
      },

      // 设置视图模式
      setViewMode: (mode) => {
        set((state) => {
          state.viewMode = mode
        })
      },

      // 导出PDF
      exportToPDF: async () => {
        try {
          // 使用浏览器的打印功能
          const printWindow = window.open('', '_blank')
          if (printWindow) {
            printWindow.document.write(`
              <!DOCTYPE html>
              <html>
                <head>
                  <title>Report Export</title>
                  <style>
                    body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                    @media print { body { margin: 0; } }
                  </style>
                </head>
                <body>
                  ${get().reportHtml}
                </body>
              </html>
            `)
            printWindow.document.close()
            printWindow.print()
            printWindow.close()
          }
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to export PDF'
          })
        }
      },

      // 重置状态
      reset: () => {
        set((state) => {
          state.reportHtml = ''
          state.reportPath = null
          state.selectedArea = null
          state.selectionRange = null
          state.isGenerating = false
          state.generationProgress = 0
          state.zoomLevel = 100
          state.viewMode = 'fit'
          state.error = null
        })
      }
    })),
    { name: 'ReportStore' }
  )
)
